using Application.Abstraction;
using Application.Abstraction.Mediator;
using Application.Exceptions;
using Application.Models;
using Application.Services;
using Application.Services.Mapper;
using SharedKernel.Models;


namespace Application.Features.Generic.Queries;

public class GetEntityByIdCachedQuery1<TDto> : GetAllEntitiesQuery<TDto>, ICachableQuery<Result<TDto>>
{
    public required int Id { get; set; }
    public string Cache<PERSON>ey { get; }
}
